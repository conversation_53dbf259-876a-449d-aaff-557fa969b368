# 梦幻西游自动化助手

一个带图形界面的梦幻西游自动化脚本，支持场景乱逛、捉鬼、挖宝等功能。

## 功能特点

- 🎮 **场景乱逛**: 自动随机方向移动角色
- 👻 **自动捉鬼**: 自动执行捉鬼任务（可扩展）
- 💎 **自动挖宝**: 自动执行挖宝任务（可扩展）
- 🖥️ **图形界面**: 友好的GUI操作界面
- 📊 **实时日志**: 显示运行状态和操作记录
- ⚙️ **参数调节**: 可调节移动速度和运行时间

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 运行脚本：
   ```bash
   python menghuan.py
   ```

2. 在界面中选择需要的功能：
   - 场景乱逛：角色自动随机移动
   - 自动捉鬼：自动执行捉鬼任务
   - 自动挖宝：自动执行挖宝任务

3. 设置参数：
   - 移动速度：每次按键的持续时间（秒）
   - 运行时间：脚本运行的总时长（分钟）

4. 点击"开始运行"，3秒内切换到梦幻西游游戏窗口

5. 脚本会自动执行选中的功能，可随时点击"停止运行"结束

## 安全提示

- 脚本使用pyautogui模拟键盘操作，请确保游戏窗口处于前台
- 建议在测试服务器或安全环境中使用
- 使用前请确认符合游戏使用条款

## 扩展功能

如需添加更多功能（如找图找色、智能避障等），可以修改以下方法：
- `scene_wander()`: 场景乱逛逻辑
- `ghost_hunt()`: 捉鬼任务逻辑  
- `treasure_dig()`: 挖宝任务逻辑

## 注意事项

- 运行前请确保已安装所有依赖包
- 建议先在小范围测试后再正式使用
- 如遇到问题，请查看日志信息进行排查 