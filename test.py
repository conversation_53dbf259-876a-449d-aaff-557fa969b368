import cv2
import numpy as np
import pyautogui
from PIL import Image, ImageDraw, ImageFont
from ultralytics import YOLO
import time
import sys
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# 根据窗口标题查找窗口位置和大小（适用于Windows系统）
def find_window_position(window_title):
    try:
        import win32gui
        import win32con
        import win32api

        # 定义窗口回调函数
        def callback(hwnd, extra):
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if window_title in title:
                    extra.append((hwnd, title))

        # 查找所有可见窗口
        windows = []
        win32gui.EnumWindows(callback, windows)

        if not windows:
            logger.warning(f"未找到标题包含 '{window_title}' 的窗口")
            return None, None

        # 选择第一个匹配的窗口
        hwnd, title = windows[0]
        logger.info(f"找到窗口: {title}")

        # 获取窗口位置和大小
        rect = win32gui.GetWindowRect(hwnd)
        x, y, right, bottom = rect
        width = right - x
        height = bottom - y

        # 调整窗口位置（减去窗口边框）
        style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
        if style & win32con.WS_OVERLAPPEDWINDOW:
            x += win32api.GetSystemMetrics(win32con.SM_CXFRAME)
            y += win32api.GetSystemMetrics(win32con.SM_CYCAPTION) + win32api.GetSystemMetrics(win32con.SM_CYFRAME)
            width -= 2 * win32api.GetSystemMetrics(win32con.SM_CXFRAME)
            height -= win32api.GetSystemMetrics(win32con.SM_CYCAPTION) + 2 * win32api.GetSystemMetrics(
                win32con.SM_CYFRAME)

        logger.info(f"窗口位置: x={x}, y={y}, width={width}, height={height}")
        return (x, y, width, height), hwnd

    except ImportError:
        logger.error("找不到win32gui模块，请安装pywin32库: pip install pywin32")
        return None, None
    except Exception as e:
        logger.error(f"查找窗口时出错: {e}")
        return None, None


class ScreenDetector:
    def __init__(self):
        self.window_title = "梦幻西游 ONLINE"
        self.window_position, self.hwnd = find_window_position(self.window_title)

        if not self.window_position:
            logger.warning("未找到目标窗口，将使用全屏检测")

        # 加载模型
        self.model_path = 'best.pt'  # 替换为你的模型路径
        self.model = YOLO(self.model_path)
        logger.info(f"已加载模型: {self.model_path}")

        # 初始化性能计数器
        self.frame_count = 0
        self.start_time = time.time()
        self.last_fps_time = time.time()

        # 创建OpenCV窗口
        cv2.namedWindow("Detection Results", cv2.WINDOW_NORMAL)
        cv2.setWindowProperty("Detection Results", cv2.WND_PROP_TOPMOST, 1)

        # 定义颜色和字体
        self.colors = [
            (255, 0, 0),  # 红色
            (0, 255, 0),  # 绿色
            (0, 0, 255),  # 蓝色
            (255, 255, 0),  # 黄色
            (255, 0, 255),  # 紫色
            (0, 255, 255)  # 青色
        ]
        # 加载中文字体
        self.font = ImageFont.truetype('simhei.ttf', 15)

    def detect_frame(self):
        try:
            frame_start_time = time.time()

            # 捕获屏幕或窗口区域
            if self.window_position:
                x, y, width, height = self.window_position
                screenshot = pyautogui.screenshot(region=(x, y, width, height))
                logger.debug(f"捕获窗口区域: x={x}, y={y}, width={width}, height={height}")
            else:
                screenshot = pyautogui.screenshot()
                logger.debug("捕获全屏幕")

            frame = np.array(screenshot)
            frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)

            # 使用模型进行检测
            detection_start = time.time()
            results = self.model(frame, verbose=False)
            detection_time = time.time() - detection_start
            logger.debug(f"检测耗时: {detection_time:.3f}秒")

            # 处理检测结果
            detections = []
            if results and results[0].boxes:
                boxes = results[0].boxes.cpu().numpy()
                logger.info(f"检测到 {len(boxes)} 个目标")

                for box in boxes:
                    xyxy = box.xyxy[0]  # 边界框坐标 [x1, y1, x2, y2]
                    cls_id = int(box.cls.item())  # 获取类别ID的标量值
                    cls = results[0].names[cls_id]  # 类别名称
                    conf = box.conf[0]  # 置信度

                    # 只保留置信度高于阈值的检测结果
                    if conf >= 0.5:
                        detections.append((xyxy, cls, conf))

                        # 计算边界框中心点
                        center_x = (xyxy[0] + xyxy[2]) / 2
                        center_y = (xyxy[1] + xyxy[3]) / 2

                        # 输出检测目标信息
                        logger.info(f"检测到目标: {cls} (置信度: {conf:.2f})")
                        logger.info(f"  位置: x1={xyxy[0]:.1f}, y1={xyxy[1]:.1f}, x2={xyxy[2]:.1f}, y2={xyxy[3]:.1f}")
                        logger.info(f"  中心点: x={center_x:.1f}, y={center_y:.1f}")
                        logger.info(f"  宽度: {xyxy[2] - xyxy[0]:.1f}, 高度: {xyxy[3] - xyxy[1]:.1f}")

                        # 在图像上绘制边界框和标签
                        color_idx = cls_id % len(self.colors)
                        color = self.colors[color_idx]
                        x1, y1, x2, y2 = map(int, xyxy)

                        # 绘制边界框
                        cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)

                        # 绘制标签背景
                        label = f"{cls}: {conf:.2f}"
                        # 使用 textbbox 计算文本宽度和高度
                        frame_pil = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                        draw = ImageDraw.Draw(frame_pil)
                        bbox = draw.textbbox((0, 0), label, font=self.font)
                        text_width = bbox[2] - bbox[0]
                        text_height = bbox[3] - bbox[1]
                        draw.rectangle((x1, y1 - text_height - 10, x1 + text_width, y1), fill=color)
                        draw.text((x1, y1 - text_height - 5), label, font=self.font, fill=(255, 255, 255))
                        # 将PIL图像转换回OpenCV图像
                        frame = cv2.cvtColor(np.array(frame_pil), cv2.COLOR_RGB2BGR)

            # 显示结果窗口
            cv2.imshow("Detection Results", frame)
            cv2.waitKey(1)  # 必须调用waitKey，否则窗口不会显示

            # 计算帧率
            self.frame_count += 1
            current_time = time.time()
            elapsed_time = current_time - self.start_time
            fps = self.frame_count / elapsed_time

            # 每10秒输出一次性能统计
            if current_time - self.last_fps_time >= 10:
                logger.info(f"性能统计: 平均FPS={fps:.1f}, 平均帧处理时间={1000 / fps:.1f}ms")
                self.last_fps_time = current_time

            # 控制帧率
            frame_time = time.time() - frame_start_time
            sleep_time = max(0, 0.05 - frame_time)  # 目标帧率约20 FPS
            time.sleep(sleep_time)

        except Exception as e:
            logger.error(f"处理帧时出错: {e}")
    def run(self):
        logger.info("开始屏幕检测...")
        try:
            while True:
                self.detect_frame()
        except KeyboardInterrupt:
            logger.info("程序被用户中断")
        finally:
            # 释放资源
            cv2.destroyAllWindows()


def main():
    try:
        detector = ScreenDetector()
        detector.run()
    except Exception as e:
        logger.error(f"程序运行出错: {e}")


if __name__ == "__main__":
    main()