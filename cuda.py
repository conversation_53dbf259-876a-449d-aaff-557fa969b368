import torch
import sys
import os
import numpy
import ultralytics
from ultralytics import YOLO


# 制作者：贤小二  更新日期：2025.6.15
def get_env_folder():
    """精确获取环境文件夹名称（如python312Cuda128）"""
    # 获取Python解释器所在目录的路径
    interpreter_dir = os.path.dirname(os.path.abspath(sys.executable))
    # 返回目录名称（环境文件夹名）
    return os.path.basename(interpreter_dir)


# 获取环境文件夹名称
folder_name = get_env_folder()
output_file = f"{folder_name}.txt"
# 创建文件并重定向输出
with open(output_file, 'w') as f:
    # 保存原始标准输出
    original_stdout = sys.stdout


    # 双输出类：同时写入文件和控制台
    class DualOutput:
        def write(self, text):
            f.write(text)
            original_stdout.write(text)

        def flush(self):
            f.flush()
            original_stdout.flush()

            # 重定向标准输出


    sys.stdout = DualOutput()

    # 执行诊断输出
    print(f'Numpy Version: {numpy.__version__}')
    print(f'Python Version: {sys.version.split()[0]}')
    print(f'PyTorch Version: {torch.__version__}')
    print(f'YOLO Status: {"Enabled" if torch.cuda.is_available() else "Disabled"}')
    print(f'YOLO Version: {ultralytics.__version__}')
    print(f'CUDA Status: {"Enabled" if torch.cuda.is_available() else "Disabled"}')
    print('CUDA Version:', torch.version.cuda if torch.cuda.is_available() else 'Disabled')
    print('GPU Count:', torch.cuda.device_count() if torch.cuda.is_available() else 0)
    print('GPU:', torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'None')

    # 恢复原始输出
    sys.stdout = original_stdout
# 控制台确认信息（不在重定向范围内）
# print(f"\n✅ 诊断报告已保存至: {os.path.abspath(output_file)}")
# print(f"📁 环境标识: {folder_name}")