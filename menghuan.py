import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import pyautogui
import random
import time
import os
from PIL import Image, ImageTk
import pygetwindow as gw
import numpy as np
import win32gui
import win32con
import win32api

import win32api

import win32api

# OCR相关导入
try:
    from paddleocr import PaddleOCR
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    print("警告: PaddleOCR未安装，OCR功能将不可用")

class MenghuanBot:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("梦幻西游自动化助手")
        self.root.geometry("600x800")
        self.root.resizable(False, False)
        
        # 运行状态
        self.is_running = False
        self.current_task = None
        self.in_battle = False  # 战斗状态标志
        
        # OCR检测区域配置 (相对于窗口的坐标)
        self.ocr_regions = {
            "战斗检测": (527, 64, 75, 33),  # (x, y, width, height)
            "地图检测": (100, 100, 200, 150),
            "系统检测": (800, 50, 150, 100)
        }
        
        # 地图坐标字典 - 存储不同地图的乱点坐标
        self.map_coordinates = {
            "长寿郊外": [
                (345, 358), (614, 440), (598, 565), (353, 411), (516, 500)
            ],
            "傲来国": [
                (80, 120), (160, 140), (120, 160), (200, 100), (140, 180),
                (100, 80), (180, 160), (130, 130), (190, 110), (110, 190)
            ],
            "建邺城": [
                (90, 130), (170, 150), (130, 170), (210, 110), (150, 190),
                (110, 90), (190, 170), (140, 140), (200, 120), (120, 200)
            ],
            "龙宫": [
                (70, 110), (150, 130), (110, 150), (190, 90), (130, 170),
                (90, 70), (170, 150), (120, 120), (180, 100), (100, 180)
            ],
            "普陀山": [
                (85, 125), (165, 145), (125, 165), (205, 105), (145, 185),
                (105, 85), (185, 165), (135, 135), (195, 115), (115, 195)
            ],
            "花果山": [
                (95, 135), (175, 155), (135, 175), (215, 115), (155, 195),
                (115, 95), (195, 175), (145, 145), (205, 125), (125, 205)
            ]
        }
        
        # 当前地图名称（可以手动设置或自动检测）
        self.current_map = "长安城"  # 默认地图
        
        # 字库识别功能（可以添加自定义字库）
        self.text_templates = {
            "战斗": ["战斗", "攻击", "回合", "HP", "MP"],
            "地图": ["地图", "小地图", "坐标"],
            "系统": ["系统", "设置", "退出"]
        }
        
        # 创建界面
        self.create_widgets()
        
        # OCR功能初始化
        self.ocr = None
        if OCR_AVAILABLE:
            try:
                # 指定本地模型路径，避免重复下载
                model_dir = os.path.expanduser("~/.paddlex/official_models")
                self.ocr = PaddleOCR(
                    use_textline_orientation=True, 
                    lang='ch',
                    det_model_dir=os.path.join(model_dir, "ch_PP-OCRv4_det_infer"),
                    rec_model_dir=os.path.join(model_dir, "ch_PP-OCRv4_rec_infer"),
                    #cls_model_dir=os.path.join(model_dir, "ch_ppocr_mobile_v2.0_cls_infer")
                )
                self.log_message("OCR功能初始化成功")
            except Exception as e:
                # 如果指定路径失败，尝试默认初始化
                try:
                    self.ocr = PaddleOCR(use_textline_orientation=True, lang='ch')
                    self.log_message("OCR功能初始化成功（使用默认路径）")
                except Exception as e2:
                    self.log_message(f"OCR初始化失败: {e2}")
                    self.ocr = None
        
    def create_widgets(self):
        # 主标题
        title_label = tk.Label(self.root, text="梦幻西游自动化助手", font=("微软雅黑", 16, "bold"))
        title_label.pack(pady=10)
        
        # 窗口查找区域
        window_frame = ttk.LabelFrame(self.root, text="梦幻西游窗口检测", padding=10)
        window_frame.pack(fill="x", padx=10, pady=5)
        self.window_info_var = tk.StringVar(value="未检测")
        self.window_info_label = tk.Label(window_frame, textvariable=self.window_info_var, fg="blue")
        self.window_info_label.pack(side="left", padx=5)
        self.refresh_window_btn = ttk.Button(window_frame, text="查找窗口", command=self.find_mhxy_window)
        self.refresh_window_btn.pack(side="left", padx=5)
        
        # 功能选择框架
        function_frame = ttk.LabelFrame(self.root, text="功能选择", padding=10)
        function_frame.pack(fill="x", padx=10, pady=5)
        
        # 场景乱逛
        self.scene_var = tk.BooleanVar()
        scene_check = ttk.Checkbutton(function_frame, text="场景乱逛", variable=self.scene_var)
        scene_check.grid(row=0, column=0, sticky="w", padx=5)
        
        # 战斗检测
        self.battle_detect_var = tk.BooleanVar(value=True)
        battle_check = ttk.Checkbutton(function_frame, text="战斗检测", variable=self.battle_detect_var)
        battle_check.grid(row=0, column=1, sticky="w", padx=5)
        
        # OCR功能
        self.ocr_var = tk.BooleanVar(value=OCR_AVAILABLE)
        ocr_check = ttk.Checkbutton(function_frame, text="OCR识别", variable=self.ocr_var, state="disabled" if not OCR_AVAILABLE else "normal")
        ocr_check.grid(row=0, column=2, sticky="w", padx=5)
        
        # 捉鬼功能
        self.ghost_var = tk.BooleanVar()
        ghost_check = ttk.Checkbutton(function_frame, text="自动捉鬼", variable=self.ghost_var)
        ghost_check.grid(row=0, column=3, sticky="w", padx=5)
        
        # 挖宝功能
        self.treasure_var = tk.BooleanVar()
        treasure_check = ttk.Checkbutton(function_frame, text="自动挖宝", variable=self.treasure_var)
        treasure_check.grid(row=0, column=4, sticky="w", padx=5)
        
        # 自动恢复血量
        self.heal_var = tk.BooleanVar()
        heal_check = ttk.Checkbutton(function_frame, text="自动恢复血量", variable=self.heal_var)
        heal_check.grid(row=1, column=0, sticky="w", padx=5)
        
        # 参数设置框架
        param_frame = ttk.LabelFrame(self.root, text="参数设置", padding=10)
        param_frame.pack(fill="x", padx=10, pady=5)
        
        # 当前地图选择
        tk.Label(param_frame, text="当前地图:").grid(row=0, column=0, sticky="w", padx=5)
        self.map_var = tk.StringVar(value=self.current_map)
        map_combo = ttk.Combobox(param_frame, textvariable=self.map_var, 
                                values=list(self.map_coordinates.keys()), 
                                state="readonly", width=15)
        map_combo.grid(row=0, column=1, sticky="w", padx=5)
        map_combo.bind('<<ComboboxSelected>>', self.on_map_changed)
        
        # 移动速度
        tk.Label(param_frame, text="移动速度:").grid(row=1, column=0, sticky="w", padx=5)
        self.speed_var = tk.StringVar(value="0.5")
        speed_entry = ttk.Entry(param_frame, textvariable=self.speed_var, width=10)
        speed_entry.grid(row=1, column=1, sticky="w", padx=5)
        tk.Label(param_frame, text="秒").grid(row=1, column=2, sticky="w")
        
        # 运行时间
        tk.Label(param_frame, text="运行时间:").grid(row=2, column=0, sticky="w", padx=5)
        self.duration_var = tk.StringVar(value="60")
        duration_entry = ttk.Entry(param_frame, textvariable=self.duration_var, width=10)
        duration_entry.grid(row=2, column=1, sticky="w", padx=5)
        tk.Label(param_frame, text="分钟").grid(row=2, column=2, sticky="w")
        
        # OCR区域配置框架
        ocr_frame = ttk.LabelFrame(self.root, text="OCR检测区域配置", padding=10)
        ocr_frame.pack(fill="x", padx=10, pady=5)
        
        # 战斗检测区域
        tk.Label(ocr_frame, text="战斗检测区域:").grid(row=0, column=0, sticky="w", padx=5)
        self.battle_x_var = tk.StringVar(value="527")
        self.battle_y_var = tk.StringVar(value="64")
        self.battle_w_var = tk.StringVar(value="75")
        self.battle_h_var = tk.StringVar(value="33")
        
        ttk.Entry(ocr_frame, textvariable=self.battle_x_var, width=5).grid(row=0, column=1, padx=2)
        ttk.Entry(ocr_frame, textvariable=self.battle_y_var, width=5).grid(row=0, column=2, padx=2)
        ttk.Entry(ocr_frame, textvariable=self.battle_w_var, width=5).grid(row=0, column=3, padx=2)
        ttk.Entry(ocr_frame, textvariable=self.battle_h_var, width=5).grid(row=0, column=4, padx=2)
        tk.Label(ocr_frame, text="(x,y,w,h)").grid(row=0, column=5, sticky="w", padx=5)
        
        # 更新区域按钮
        update_region_btn = ttk.Button(ocr_frame, text="更新区域", command=self.update_ocr_regions)
        update_region_btn.grid(row=0, column=6, padx=10)
        
        # 控制按钮框架
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill="x", padx=10, pady=10)
        
        # 开始按钮
        self.start_btn = ttk.Button(control_frame, text="开始运行", command=self.start_bot)
        self.start_btn.pack(side="left", padx=5)
        
        # 停止按钮
        self.stop_btn = ttk.Button(control_frame, text="停止运行", command=self.stop_bot, state="disabled")
        self.stop_btn.pack(side="left", padx=5)
        
        # 状态显示
        self.status_label = tk.Label(control_frame, text="状态: 待机中", fg="blue")
        self.status_label.pack(side="right", padx=5)
        
        # 日志显示框架
        log_frame = ttk.LabelFrame(self.root, text="运行日志", padding=10)
        log_frame.pack(fill="both", expand=True, padx=10, pady=5)

        self.log_text = scrolledtext.ScrolledText(
            log_frame, height=15, width=80, bg="#222", fg="#00FF00", font=("Consolas", 12)
        )
        self.log_text.pack(fill="both", expand=True, padx=2, pady=2)
        self.log_text.config(state="normal")
        self.log_text.insert(tk.END, "日志控件初始化成功\n")
        self.log_text.config(state="normal")

        # 清空日志按钮
        clear_btn = ttk.Button(log_frame, text="清空日志", command=self.clear_log)
        clear_btn.pack(pady=5, anchor="e")

        # YOLO检测控制
        yolo_frame = ttk.LabelFrame(self.root, text="YOLO检测控制", padding=10)
        yolo_frame.pack(fill="x", padx=10, pady=5)
        
        self.start_yolo_btn = ttk.Button(yolo_frame, text="开始YOLO检测", command=self.start_yolo_detection)
        self.start_yolo_btn.pack(side="left", padx=5)
        
        self.stop_yolo_btn = ttk.Button(yolo_frame, text="停止YOLO检测", command=self.stop_yolo_detection, state="disabled")
        self.stop_yolo_btn.pack(side="left", padx=5)
        
        # YOLO检测器初始化
        self.yolo_detector = YoloDetector()
        self.yolo_detector.init_model()
        
        if not self.yolo_detector.model:
            self.log_message("YOLO模型加载失败")
        else:
            self.log_message("YOLO模型加载成功")
        
    def log_message(self, message):
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
        def _write():
            self.log_text.config(state="normal")
            self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.log_text.see(tk.END)
            self.log_text.config(state="normal")
        # 保证在主线程执行
        self.root.after(0, _write)
        
    def clear_log(self):
        """清空日志"""
        self.log_text.config(state="normal")
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state="normal")
        
    def start_bot(self):
        """开始运行机器人"""
        if not any([self.scene_var.get(), self.ghost_var.get(), self.treasure_var.get(), self.heal_var.get()]):
            messagebox.showwarning("警告", "请至少选择一个功能！")
            return
            
        self.is_running = True
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.status_label.config(text="状态: 运行中", fg="green")
        
        # 在新线程中运行
        self.bot_thread = threading.Thread(target=self.run_bot)
        self.bot_thread.daemon = True
        self.bot_thread.start()
        
    def stop_bot(self):
        """停止运行机器人"""
        self.is_running = False
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.status_label.config(text="状态: 已停止", fg="red")
        self.log_message("已停止运行")
        
    def run_bot(self):
        """运行机器人的主要逻辑"""
        self.log_message("开始运行梦幻西游自动化助手")
        self.log_message("3秒后开始，请切换到游戏窗口")
        time.sleep(3)
        
        try:
            duration = int(self.duration_var.get()) * 60  # 转换为秒
            speed = float(self.speed_var.get())
            start_time = time.time()
            
            while self.is_running and (time.time() - start_time) < duration:
                if self.scene_var.get():
                    self.scene_wander(speed)
                if self.ghost_var.get():
                    self.ghost_hunt()
                if self.treasure_var.get():
                    self.treasure_dig()
                if self.heal_var.get():
                    self.auto_heal()

                    
                time.sleep(1)
                
        except Exception as e:
            self.log_message(f"运行出错: {str(e)}")
        finally:
            self.stop_bot()
            
    def scene_wander(self, speed):
        """场景乱逛功能"""
        # 先激活窗口
        if not self.activate_mhxy_window():
            return
            
        # 摁住Tab键显示小地图
        self.log_message("摁住Tab键显示小地图")
        pyautogui.keyDown('tab')
        time.sleep(0.5)  # 等待小地图显示
        pyautogui.keyUp('tab')
        time.sleep(0.3)
        
        # 在小地图中随机点击（窗口相对坐标）
        if self.current_map in self.map_coordinates:
            coordinates = self.map_coordinates[self.current_map]
            if coordinates:
                x, y = random.choice(coordinates)
                # 获取窗口左上角坐标
                if hasattr(self, 'mhxy_window') and self.mhxy_window:
                    win_left, win_top = self.mhxy_window.left, self.mhxy_window.top
                    abs_x, abs_y = win_left + x, win_top + y
                    self.log_message(f"场景乱逛: 在{self.current_map}小地图中点击窗口相对坐标 ({x}, {y})，实际点击屏幕坐标 ({abs_x}, {abs_y})")
                    pyautogui.click(abs_x, abs_y)
                    time.sleep(1)
                    pyautogui.keyDown('tab')
                    time.sleep(0.5)  # 等待小地图显示
                    pyautogui.keyUp('tab')
                    time.sleep(0.3)

                    # 检测是否进入战斗
                    if self.battle_detect_var.get() and self.check_battle_status():
                        self.log_message("检测到战斗，等待战斗结束...")
                        if not self.wait_for_battle_end():
                            return  # 如果脚本被停止，直接返回
                        # 战斗结束后重新打开地图
                        self.log_message("战斗结束，重新打开地图")
                        pyautogui.keyDown('tab')
                        time.sleep(0.5)
                        pyautogui.keyUp('tab')
                        time.sleep(0.3)
                    
                    time.sleep(15)  # 点击后等待15秒
                else:
                    self.log_message("未找到梦幻西游窗口，无法进行相对坐标点击。")
            else:
                self.log_message(f"当前地图 '{self.current_map}' 没有坐标点。")
        else:
            self.log_message(f"当前地图 '{self.current_map}' 不在坐标字典中。")
        
    def ghost_hunt(self):
        """捉鬼功能"""
        self.log_message("执行捉鬼任务")
        # 这里可以添加捉鬼的具体逻辑
        # 比如点击捉鬼按钮、选择目标等
        time.sleep(2)
        
    def treasure_dig(self):
        """挖宝功能"""
        self.log_message("执行挖宝任务")
        # 这里可以添加挖宝的具体逻辑
        # 比如点击挖宝按钮、选择地点等
        time.sleep(2)
        
    def find_mhxy_window(self):
        """查找梦幻西游窗口并显示信息（排除自身窗口）"""
        try:
            windows = gw.getAllWindows()
            found = False
            my_title = self.root.title()
            for w in windows:
                if "梦幻西游 ONLINE" in w.title and w.title != my_title:
                    info = f"标题: {w.title} | 位置: ({w.left},{w.top}) | 大小: {w.width}x{w.height}"
                    self.window_info_var.set(info)
                    self.log_message(f"找到窗口: {info}")
                    # 保存找到的窗口对象
                    self.mhxy_window = w
                    found = True
                    break
            if not found:
                self.window_info_var.set("未找到梦幻西游窗口")
                self.log_message("未找到梦幻西游窗口")
                self.mhxy_window = None
        except Exception as e:
            self.window_info_var.set("查找出错")
            self.log_message(f"查找窗口出错: {e}")
            self.mhxy_window = None

    def activate_mhxy_window(self):
        """激活梦幻西游窗口"""
        if hasattr(self, 'mhxy_window') and self.mhxy_window:
            try:
                self.mhxy_window.activate()
                self.log_message("已激活梦幻西游窗口")
                time.sleep(0.5)  # 等待窗口激活
                return True
            except Exception as e:
                self.log_message(f"激活窗口失败: {e}")
                return False
        else:
            self.log_message("未找到梦幻西游窗口，请先查找窗口")
            return False

    def on_map_changed(self, event):
        """地图选择改变时更新当前地图"""
        selected_map = self.map_var.get()
        if selected_map in self.map_coordinates:
            self.current_map = selected_map
            self.log_message(f"当前地图已更新为: {self.current_map}")
        else:
            self.log_message(f"警告: 地图 '{selected_map}' 不存在于坐标字典中。")

    def run(self):
        """运行GUI"""
        self.root.mainloop()

    def check_battle_status(self):
        """检测是否在战斗中"""
        try:
            if hasattr(self, 'mhxy_window') and self.mhxy_window:
                # 获取窗口左上角坐标
                win_left, win_top = self.mhxy_window.left, self.mhxy_window.top
                
                # 获取战斗检测区域
                battle_region = self.ocr_regions.get("战斗检测", (527, 64, 75, 33))
                region_x, region_y, region_width, region_height = battle_region
                
                # 截取指定区域进行检测
                screenshot = pyautogui.screenshot(region=(
                    win_left + region_x, 
                    win_top + region_y, 
                    region_width, 
                    region_height
                ))
                
                # 优先使用OCR检测
                if self.ocr and self.ocr_var.get():
                    battle_detected = self.detect_battle_by_ocr(screenshot)
                    if battle_detected:
                        return True
                
                # 如果OCR检测失败或未安装，使用颜色检测作为备选
                battle_detected = self.detect_battle_by_color(screenshot)
                
                return battle_detected
            else:
                return False
        except Exception as e:
            self.log_message(f"战斗检测出错: {e}")
            return False
    
    def detect_battle_by_color(self, screenshot):
        """通过颜色检测战斗状态（示例方法）"""
        try:
            # 转换为PIL图像
            img = screenshot
            
            # 检测特定区域的颜色（需要根据实际游戏界面调整）
            # 这里假设战斗界面在右下角有特定颜色
            width, height = img.size
            
            # 检测右下角区域的颜色（示例坐标，需要根据实际游戏调整）
            battle_area_x = width - 100
            battle_area_y = height - 100
            
            if battle_area_x > 0 and battle_area_y > 0:
                pixel_color = img.getpixel((battle_area_x, battle_area_y))
                # 这里需要根据实际战斗界面的颜色来调整
                # 示例：检测是否为红色系（战斗血条等）
                if pixel_color[0] > 200 and pixel_color[1] < 100 and pixel_color[2] < 100:
                    return True
            
            return False
        except Exception as e:
            self.log_message(f"颜色检测出错: {e}")
            return False
    
    def wait_for_battle_end(self):
        """等待战斗结束"""
        self.log_message("检测到战斗，等待战斗结束...")
        self.in_battle = True
        
        while self.in_battle and self.is_running:
            if not self.check_battle_status():
                self.log_message("战斗结束，继续乱逛")
                self.in_battle = False
                break
            time.sleep(1)  # 每秒检测一次
        
        if not self.is_running:
            return False
        
        return True

    def ocr_detect_text(self, screenshot, target_texts=None):
        """使用OCR识别图片中的文字，兼容新版PaddleOCR"""
        if not self.ocr:
            return False, []
        try:
            # 转为numpy数组
            if hasattr(screenshot, 'mode'):
                img_np = np.array(screenshot)
            else:
                img_np = screenshot
            # 新版PaddleOCR
            try:
                result = self.ocr.predict(img_np)
            except AttributeError:
                result = self.ocr.ocr(img_np, cls=True)
            detected_texts = []
            for line in result:
                if line:
                    for word_info in line:
                        if len(word_info) >= 2:
                            text = word_info[1][0]  # 识别的文字
                            confidence = word_info[1][1]  # 置信度
                            detected_texts.append((text, confidence))
            if target_texts:
                for text, conf in detected_texts:
                    if any(target in text for target in target_texts):
                        return True, detected_texts
            return False, detected_texts
        except Exception as e:
            self.log_message(f"OCR识别出错: {e}")
            return False, []
    
    def detect_battle_by_ocr(self, screenshot):
        """通过OCR检测战斗状态"""
        battle_keywords = self.text_templates.get("战斗", [])
        is_battle, detected_texts = self.ocr_detect_text(screenshot, battle_keywords)
        
        if is_battle:
            self.log_message(f"OCR检测到战斗关键词: {detected_texts}")
        
        return is_battle
    
    def detect_map_by_ocr(self, screenshot):
        """通过OCR检测地图界面"""
        map_keywords = self.text_templates.get("地图", [])
        is_map, detected_texts = self.ocr_detect_text(screenshot, map_keywords)
        
        if is_map:
            self.log_message(f"OCR检测到地图关键词: {detected_texts}")
        
        return is_map

    def update_ocr_regions(self):
        """更新OCR检测区域"""
        try:
            new_battle_x = int(self.battle_x_var.get())
            new_battle_y = int(self.battle_y_var.get())
            new_battle_w = int(self.battle_w_var.get())
            new_battle_h = int(self.battle_h_var.get())
            
            self.ocr_regions["战斗检测"] = (new_battle_x, new_battle_y, new_battle_w, new_battle_h)
            self.log_message(f"战斗检测区域已更新为: ({new_battle_x}, {new_battle_y}, {new_battle_w}, {new_battle_h})")
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的整数区域坐标和尺寸。")
        except Exception as e:
            self.log_message(f"更新区域出错: {e}")

    def auto_heal(self):
        """自动恢复血量功能"""
        # 先激活窗口
        if not self.activate_mhxy_window():
            return
            
        try:
            if hasattr(self, 'mhxy_window') and self.mhxy_window:
                # 获取窗口左上角坐标
                win_left, win_top = self.mhxy_window.left, self.mhxy_window.top
                
                # 计算绝对坐标
                abs_x = win_left + 999
                abs_y = win_top + 65
                
                self.log_message(f"自动恢复血量: 右键点击窗口相对坐标 (999, 65)，实际屏幕坐标 ({abs_x}, {abs_y})")
                
                # 右键点击指定坐标
                pyautogui.rightClick(abs_x, abs_y)
                
                time.sleep(0.5)  # 等待右键菜单出现
                x,y = pyautogui.position()
                self.log_message(f"当前鼠标坐标: ({x}, {y})")
                # 相对当前位置移动（横向+50像素，纵向+30像素）
                pyautogui.moveRel(-60, 10, duration=0.25)
                pyautogui.rightClick()
                time.sleep(3)
                self.log_message("恢复完毕，关闭自用界面")
                
            else:
                self.log_message("未找到梦幻西游窗口，无法执行自动恢复血量")
                
        except Exception as e:
            self.log_message(f"自动恢复血量出错: {e}")

    def start_yolo_detection(self):
        """开始YOLO检测"""
        if not self.yolo_detector or not self.yolo_detector.model:
            self.log_message("YOLO检测器未初始化或模型加载失败")
            return
            
        # 设置游戏窗口
        if hasattr(self, 'mhxy_window') and self.mhxy_window:
            self.yolo_detector.set_game_window(self.mhxy_window)
            # 开始窗口覆盖检测
            self.yolo_detector.start_overlay_detection(confidence=0.5, delay=0.1)
            self.start_yolo_btn.config(state="disabled")
            self.stop_yolo_btn.config(state="normal")
            self.log_message("YOLO窗口覆盖检测已开始")
        else:
            self.log_message("请先查找梦幻西游窗口")
    
    def stop_yolo_detection(self):
        """停止YOLO检测"""
        if self.yolo_detector:
            self.yolo_detector.stop_real_time_detection()
            self.start_yolo_btn.config(state="normal")
            self.stop_yolo_btn.config(state="disabled")
            self.log_message("YOLO检测已停止")
            
            # 刷新游戏窗口以清除绘制的内容
            if hasattr(self, 'mhxy_window') and self.mhxy_window:
                try:
                    hwnd = win32gui.FindWindow(None, self.mhxy_window.title)
                    if hwnd:
                        win32gui.InvalidateRect(hwnd, None, True)
                        win32gui.UpdateWindow(hwnd)
                except:
                    pass

    
    app = MenghuanBot()    
    app.run()

app = Men






ghuanBot()    
    app.run()


app = Men
    






ghuanBot()
if _app.run()


_name__ =
    






= "__main__":
    # 设置pyautogui的安全设置
    pyautogui
.FAILSAFE = True
    pyautogui.PAUSE = 0.1
    

    app = MenghuanBot()
    app.run()



