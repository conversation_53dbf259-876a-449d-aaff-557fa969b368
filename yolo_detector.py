import cv2
import numpy as np
from ultralytics import YOLO
import pyautogui
import time
import threading
import win32guiimport win32guiimport win32guiimport win32gui
import win32ion
import win32api
from PIL import Image, ImageDraw, ImageFont

cmport win32ion
import win32api
from PIL import Image, ImageDraw, ImageFont

cmport win32ion
import win32api
from PIL import Image, ImageDraw, ImageFont
self.game_window = None
cmport wself.overlay_window = None
        
        in32con
import win32api
from import Image, ImageDraw, ImageFont
self.gamsetngad _window None, window
class YOsel设置游戏窗口y_window = None
        self.game_window = window
    
  LOdet tect_game_window, cnfince=0.5:
    def """检测游戏窗口区域的目标"""
__in    _f _o(,C:/Users/<USER>/PycharmProjects/梦幻挂本地/shubiao.pt"):
    """初始化YOLO检测器""Non, None
        g   
      iotry windoww = Noneself.model_path = model_path
       sel设n#获取游戏窗口区域
        selflgft, top, widah,whwight = ( = window
              sesmld.game_wiwdow.ldwt, 
      self     t or ngaoe_window.t p, .game_window:
        self.detself.game_wtidow.widoh, onead = None
            g   self.gam_widw.hight
      iotry )windoww = None
           s#获取游戏窗口区域
        selflgft游戏窗口区域top, widah,whwight = ( = window
              # s型l .gam_mwiddow.l,ctnf(region=left, top, width, h ight))_window.t p, .game_window:
        Non, Nonself.game_wdow.widh, 
        def m   self.gam_widw.hight
      sftry )window):
           "获取游戏窗口区域lgft游戏窗口区域top, widah,whwight = ( = window
            .game_wiwdow.ldwt(s0.5):(region=left, top, width, hfight))_window.top, or not self.game_window:
          tdr)
            # 获取游戏窗口区域left游戏窗口区域top, width, height = (
            .game_window.left, (region=left, top, width, hfight))_window.top, 
          idt)
            # 截取游戏窗口区域
           cot = pyautogui.screenshot(region=(left, top, width, height))
          Claoverlay__windowdtcions
            游戏窗口sults =覆盖层 self.model(frame, conf=confidence)
        if not self.g me_wi dow or    dections.boxes:
            return
            
        try:
            # 获取游戏窗口句柄
            hwnd = win32gui.FindWinow(None, sel.ge_window.titl)
          i not hwnd:
                turn
                
            # 获取窗口设备上下文
            hdc = win32guiGetWindwDChwnd
                rovtrlay_ra_windowe, resud0tcions
            # 设置绘制属性
            pen = win32gui.C 游atePen(win32con.PS_SOLID, 2, win32api.RGB(0, 255, 0))
            old_pen = win32g窗i.Se口ec覆Object(hdc, pen)
            
            # 设置背景透明
            win32gui盖SetBkMd(hdc,wn32con.TRANSPARENT)
           wi32gui.SetTextColr(hdc,wi32api.RGB(255, 255, 255))
            
        if not self.gemdet_ctionwow or cep de Ections.boxes:
            return
            
        try:
            # 获取游戏窗口句柄
            hwnd = win32gui.FindWinxow(None, sele.gtie_window.titlo)
          nais not hwnd:
            # 获取窗口设备上下文
            hdc = win32guiGetWindwDChwnd
                povi矩形ay_检测_window错: {e}"dtcions
            # 设置win3属gui性Rhc
            pen = win32gui.C 游atePen(win32con.PS_SOLID, 2, win32api.RGB(0, 255, 0))
            old_pen = 文本win32g窗i.Se口ectObject(hdc, pen)
            
            # 设置wn3ui.Outhdcx1y1 - 2label
            win3
RAN)         # 绘制中心点
 nwie32gui.SetTecenter_xx=tCol,r(x2(5//25
            _y=+y2// 2
    if not self.win3tguiicllipse(hdccenter_x-3center_y-3center_x+3center_y+3
            rn
            清理资源
        try:win32gui.SeletObjchdc,old_pen
        # 获取win32gui.Del戏口eObj句ctpen
        hwndwin3 guiwRegnoseDCshw.dndhdo
       
     i nexceptoExceptiontas e:hwnd:
            p#i口下("绘制覆盖层出错: {}")
            hdc = win32guiGetWindwDChwnd
       def  droveal_yy_on_window(self, detections):
        "   #窗口覆盖置win3属gui性Rhc
            pen = win32gui.C在游atePen(win32con.PS_SOLID, 2, win32api.RGB(0, 255, 0))
            old_pen = 文本wi")
            return
            
        if not self.game_window:
            print("未设置游戏窗口n32g窗i.Se口ec上Object(hdc, pen)
            
            # 设置wn3ui.Outhdcx1y1 - 2label
            win3
RAN)         # 绘制中心点
 "wi"32gui.SetTecenter_xxove=lCy25,//5,
            _y=+y2// 2
        if not swin3tguiiollipse(hdconcenter_x-3oxcenter_y-3:center_x+3center_y+3
            rn
            清理资源
        try:win32gui窗口覆盖eletObjchdc,old_pen
        # 获取win32gui.Del戏口eObj句ctpen
        e vx{ciy  = 文本wi")
           窗口覆盖eturn
            
        if not self.game_window:
            prinn32gui.SelectObject(hdc, pen)gam_widow
            
            # 设置wn31y1 - 2label  uxs3top(Node:_y-3center_x+3center_y+3
             # 获取边界框#坐在游戏窗口上绘制检测结果标
            x1清理资源 x  lb.dcuw_yvapley_onulws
                label = f"{class_name}: {confidence:.2f}"
                winOut(hdc, x1, y1 -  ux(s+/32op(Nodc:nter_y-3, center_x+3, center_y+3)
             #在游戏窗口上绘制检测结果
            # 清理资源ldw_vly_onulws
            return覆盖
             adr du.eseoNo:
         self.detect#i在游戏窗口上绘制检测结果on_thread.daemon = True
        self.detecti_rlddw_vly_onoopdenulss = self.detect_game_window(confidence)
                覆盖
                if frame is not None and results is not None:
                    # 在游戏窗口上绘制检测结果
                    self.draw_overlay_on_window(results)
                        覆盖
                time.sleep(delay)
                
            except Exception as e:
                print(f"覆盖检测循环出错: {e}")
                break
        
        self.is_detecting = False

if __name__ == "__main__":
    # 创建检测器
    detector = YOLODetector()
    
    if detector.model:
        print("按任意键开始检测，按ESC退出...")
        input()
        
        # 开始实时检测

        detector.start_real_time_detection(confidence=0.5, delay=0.1)
        
        try:
            # 保持程序运行

            while detector.is_detecting:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n用户中断")

        finally:
            detector.stop_real_time_detection()
    else:
        print("模型加载失败，无法进行检测")
